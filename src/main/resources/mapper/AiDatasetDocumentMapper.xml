<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiDatasetDocumentMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiDatasetDocument">
    <!--@mbg.generated-->
    <!--@Table ai_dataset_document-->
    <id column="doc_id" jdbcType="VARCHAR" property="docId" />
    <result column="dataset_id" jdbcType="VARCHAR" property="datasetId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="doc_url" jdbcType="VARCHAR" property="docUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="info_id" jdbcType="VARCHAR" property="infoId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    doc_id, dataset_id, `name`, doc_url, `status`, create_by, create_time, info_id, update_time, 
    update_by, remark
  </sql>
</mapper>