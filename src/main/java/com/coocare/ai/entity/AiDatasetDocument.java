package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库文章
 */
@Schema(description="知识库文章")
@Data
@TableName(value = "ai_dataset_document")
public class AiDatasetDocument implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "doc_id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private String docId;

    @TableField(value = "dataset_id")
    @Schema(description="")
    private String datasetId;

    /**
     * 文件名
     */
    @TableField(value = "`name`")
    @Schema(description="文件名")
    private String name;

    /**
     * 文件下载地址
     */
    @TableField(value = "doc_url")
    @Schema(description="文件下载地址")
    private String docUrl;

    /**
     * 文件状态
     */
    @TableField(value = "`status`")
    @Schema(description="文件状态")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * dify库中的ID
     */
    @TableField(value = "info_id")
    @Schema(description="dify库中的ID")
    private String infoId;

    @TableField(value = "update_time")
    @Schema(description="")
    private LocalDateTime updateTime;

    @TableField(value = "update_by")
    @Schema(description="")
    private String updateBy;

    /**
     * 特定标识
     */
    @TableField(value = "remark")
    @Schema(description="特定标识")
    private String remark;
}